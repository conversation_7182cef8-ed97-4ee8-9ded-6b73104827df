'use client'

import { useState } from 'react'
import { FilterOptions, ProductCategory } from '@/types'
import { colors, sizes } from '@/data/products'

interface ProductFiltersProps {
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  productCount: number
}

export default function ProductFilters({ filters, onFiltersChange, productCount }: ProductFiltersProps) {
  const [isOpen, setIsOpen] = useState(false)

  const categories: { id: ProductCategory; label: string }[] = [
    { id: 'tshirts', label: 'T-shirts' },
    { id: 'hoodies', label: 'Hoodies' },
    { id: 'accessories', label: 'Accessoires' },
    { id: 'stickers', label: 'Stickers' },
    { id: 'mugs', label: 'Mugs' },
    { id: 'posters', label: 'Posters' },
  ]

  const priceRanges = [
    { label: 'Moins de 20€', min: 0, max: 20 },
    { label: '20€ - 40€', min: 20, max: 40 },
    { label: '40€ - 60€', min: 40, max: 60 },
    { label: '60€ - 80€', min: 60, max: 80 },
    { label: 'Plus de 80€', min: 80, max: 1000 },
  ]

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    onFiltersChange({ ...filters, ...newFilters })
  }

  const clearFilters = () => {
    onFiltersChange({ sortBy: 'name', sortOrder: 'asc' })
  }

  const hasActiveFilters = () => {
    return !!(
      filters.category?.length ||
      filters.priceRange ||
      filters.sizes?.length ||
      filters.colors?.length ||
      filters.inStock !== undefined ||
      filters.featured !== undefined
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Mobile toggle */}
      <div className="lg:hidden">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between p-4 text-left"
        >
          <span className="font-semibold text-gray-900">Filtres</span>
          <svg
            className={`w-5 h-5 transform transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      <div className={`${isOpen ? 'block' : 'hidden'} lg:block`}>
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Filtres</h3>
            {hasActiveFilters() && (
              <button
                onClick={clearFilters}
                className="text-sm text-primary-600 hover:text-primary-700 font-medium"
              >
                Tout effacer
              </button>
            )}
          </div>

          {/* Results count */}
          <div className="mb-6 p-3 bg-gray-50 rounded-lg">
            <span className="text-sm text-gray-600">
              {productCount} résultat{productCount !== 1 ? 's' : ''}
            </span>
          </div>

          {/* Categories */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Catégories</h4>
            <div className="space-y-2">
              {categories.map((category) => (
                <label key={category.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.category?.includes(category.id) || false}
                    onChange={(e) => {
                      const currentCategories = filters.category || []
                      if (e.target.checked) {
                        updateFilters({
                          category: [...currentCategories, category.id]
                        })
                      } else {
                        updateFilters({
                          category: currentCategories.filter(c => c !== category.id)
                        })
                      }
                    }}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{category.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Prix</h4>
            <div className="space-y-2">
              {priceRanges.map((range, index) => (
                <label key={index} className="flex items-center">
                  <input
                    type="radio"
                    name="priceRange"
                    checked={
                      filters.priceRange?.[0] === range.min && 
                      filters.priceRange?.[1] === range.max
                    }
                    onChange={() => {
                      updateFilters({
                        priceRange: [range.min, range.max]
                      })
                    }}
                    className="border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">{range.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Sizes */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Tailles</h4>
            <div className="grid grid-cols-3 gap-2">
              {sizes.map((size) => (
                <button
                  key={size.id}
                  onClick={() => {
                    const currentSizes = filters.sizes || []
                    if (currentSizes.includes(size.id)) {
                      updateFilters({
                        sizes: currentSizes.filter(s => s !== size.id)
                      })
                    } else {
                      updateFilters({
                        sizes: [...currentSizes, size.id]
                      })
                    }
                  }}
                  className={`p-2 text-sm border rounded-lg text-center transition-colors ${
                    filters.sizes?.includes(size.id)
                      ? 'border-primary-600 bg-primary-50 text-primary-600'
                      : 'border-gray-300 text-gray-700 hover:border-gray-400'
                  }`}
                >
                  {size.name}
                </button>
              ))}
            </div>
          </div>

          {/* Colors */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Couleurs</h4>
            <div className="grid grid-cols-4 gap-2">
              {colors.map((color) => (
                <button
                  key={color.id}
                  onClick={() => {
                    const currentColors = filters.colors || []
                    if (currentColors.includes(color.id)) {
                      updateFilters({
                        colors: currentColors.filter(c => c !== color.id)
                      })
                    } else {
                      updateFilters({
                        colors: [...currentColors, color.id]
                      })
                    }
                  }}
                  className={`w-10 h-10 rounded-full border-2 transition-all ${
                    filters.colors?.includes(color.id)
                      ? 'border-primary-600 scale-110'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  style={{ backgroundColor: color.hex }}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Stock Status */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Disponibilité</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.inStock === true}
                  onChange={(e) => {
                    updateFilters({
                      inStock: e.target.checked ? true : undefined
                    })
                  }}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">En stock uniquement</span>
              </label>
            </div>
          </div>

          {/* Featured */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Spécial</h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.featured === true}
                  onChange={(e) => {
                    updateFilters({
                      featured: e.target.checked ? true : undefined
                    })
                  }}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-700">Produits populaires</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
