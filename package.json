{"name": "vigovibe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "autoprefixer": "^10.4.21", "framer-motion": "^12.14.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.3.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}