import Link from 'next/link'
import Image from 'next/image'
import { Event } from '@/types'

interface EventCardProps {
  event: Event
}

export default function EventCard({ event }: EventCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      sports: 'bg-blue-500',
      music: 'bg-purple-500',
      politics: 'bg-red-500',
      culture: 'bg-green-500',
      holidays: 'bg-yellow-500',
      festivals: 'bg-pink-500',
    }
    return colors[category as keyof typeof colors] || 'bg-gray-500'
  }

  const getCategoryLabel = (category: string) => {
    const labels = {
      sports: 'Sports',
      music: 'Music',
      politics: 'Politics',
      culture: 'Culture',
      holidays: 'Holidays',
      festivals: 'Festivals',
    }
    return labels[category as keyof typeof labels] || category
  }

  return (
    <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover-lift transition-all duration-300">
      <div className="relative aspect-video overflow-hidden">
        <Image
          src={event.image}
          alt={event.name}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
        />

        {/* Category badge */}
        <div className="absolute top-4 left-4">
          <span className={`${getCategoryColor(event.category)} text-white px-3 py-1 rounded-full text-sm font-semibold`}>
            {getCategoryLabel(event.category)}
          </span>
        </div>

        {/* Featured badge */}
        {event.featured && (
          <div className="absolute top-4 right-4">
            <span className="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              Featured
            </span>
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-60 transition-opacity duration-300" />

        {/* Quick action */}
        <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Link
            href={`/events/${event.id}`}
            className="bg-white text-gray-900 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
          >
            View Event
          </Link>
        </div>
      </div>

      <div className="p-6">
        <div className="mb-3">
          <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-200 mb-2">
            <Link href={`/events/${event.id}`}>
              {event.name}
            </Link>
          </h3>
          <p className="text-gray-600 line-clamp-2">
            {event.description}
          </p>
        </div>

        {/* Event details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {formatDate(event.date)}
          </div>
          <div className="flex items-center text-sm text-gray-500">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {event.location}
          </div>
        </div>

        {/* Products count */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm text-gray-500">
            {event.products?.length || 0} product{(event.products?.length || 0) !== 1 ? 's' : ''} available
          </span>
          <div className="flex items-center text-sm text-gray-500">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Add to favorites
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-3">
          <Link
            href={`/events/${event.id}`}
            className="flex-1 bg-primary-600 text-white py-3 px-4 rounded-lg font-semibold text-center hover:bg-primary-700 transition-colors duration-200"
          >
            Discover
          </Link>
          <Link
            href={`/shop?event=${event.id}`}
            className="flex-1 border-2 border-primary-600 text-primary-600 py-3 px-4 rounded-lg font-semibold text-center hover:bg-primary-600 hover:text-white transition-all duration-200"
          >
            View Products
          </Link>
        </div>
      </div>
    </div>
  )
}
